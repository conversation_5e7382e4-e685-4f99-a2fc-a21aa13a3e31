[package]
name = "repx"
version = "0.1.0"
edition = "2021"
description = "Tool for reproducible builds using Nix inside Docker containers"
license = "MIT"
repository = "https://github.com/GHawk1124/repx"
readme = "README.md"
keywords = ["nix", "docker", "build", "reproducible", "cross-compilation"]
categories = ["development-tools::build-utils", "command-line-utilities"]
include = [
    "src/**/*",
    "templates/**/*",
    "Cargo.toml",
    "README.md"
]

[dependencies]
anyhow = "1.0.98"
bollard = "0.19.0"
cargo_metadata = "0.20.0"
clap = { version = "4.5.38", features = ["derive"] }
futures-util = "0.3.31"
serde = { version = "1.0.219", features = ["derive"] }
tera = "1.20.0"
tokio = { version = "1.45.0", features = ["full"] }
uuid = { version = "1.7.0", features = ["v4"] }

[profile.release]
lto = true
opt-level = "s"
codegen-units = 1
panic = "abort"
strip = true

[lib]
name = "repx_lib"
path = "src/lib.rs"

[[bin]]
name = "repx"
path = "src/main.rs"
